"""
后台任务执行器

负责从队列中获取任务并执行
"""

import asyncio
from datetime import datetime

from app.logger import logger
from .state_manager import state_manager
from .agent_adapter import SimpleAgentAdapter


# 全局 Agent 适配器实例
agent_adapter = SimpleAgentAdapter(state_manager)


async def task_executor():
    """后台任务执行器"""
    logger.info("启动后台任务执行器")

    while True:
        try:
            # 检查所有会话的任务队列
            for session_id, queue in state_manager.task_queues.items():
                try:
                    # 非阻塞检查队列
                    task_id = queue.get_nowait()

                    # 启动任务执行
                    task = asyncio.create_task(
                        agent_adapter.execute_task(task_id)
                    )
                    agent_adapter.running_tasks[task_id] = task

                    logger.info(f"启动任务执行: {task_id}")

                except asyncio.QueueEmpty:
                    continue
                except Exception as e:
                    logger.error(f"任务队列处理错误: {e}")

        except Exception as e:
            logger.error(f"任务执行器错误: {e}")

        await asyncio.sleep(0.1)  # 避免CPU占用过高


async def cleanup_task():
    """定期清理过期数据"""
    logger.info("启动数据清理任务")

    while True:
        try:
            await asyncio.sleep(3600)  # 每小时清理一次
            await state_manager.cleanup_expired()
            logger.info("完成过期数据清理")
        except Exception as e:
            logger.error(f"数据清理错误: {e}")


async def start_background_tasks():
    """启动所有后台任务"""
    logger.info("启动后台服务")

    # 初始化状态管理器（处理重新排队）
    await state_manager.initialize_after_startup()

    # 创建后台任务
    tasks = [
        asyncio.create_task(task_executor()),
        asyncio.create_task(cleanup_task())
    ]

    logger.info("后台任务已启动")
    return tasks
