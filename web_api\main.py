"""
Web API 主应用模块（简化版）
"""
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from .config import config
from .routers import sessions, tasks, tools, websocket, agent_detail
from .services.background import start_background_tasks
from .services.state_manager import state_manager


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print("🚀 启动后台任务...")
    await start_background_tasks()
    print("✅ 后台任务启动完成")

    yield

    # 关闭时执行
    print("🧹 清理资源...")
    print("✅ 清理完成")


def create_app() -> FastAPI:
    """创建 FastAPI 应用实例"""
    app = FastAPI(
        title=config.title,
        description=config.description,
        version=config.version,
        openapi_url=config.openapi_url,
        docs_url=config.docs_url,
        redoc_url=config.redoc_url,
        lifespan=lifespan
    )

    # CORS配置（开发用）
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 路由注册
    app.include_router(sessions.router, prefix="/api")
    app.include_router(tasks.router, prefix="/api")
    app.include_router(tools.router, prefix="/api")
    app.include_router(agent_detail.router, prefix="/api")
    app.include_router(websocket.router, prefix="/ws")

    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": "RPA Agent 简化版 API",
            "version": config.version,
            "docs": "/docs",
            "websocket": "/ws/sessions/{session_id}",
            "status": "running"
        }

    @app.get("/api/health")
    async def health_check():
        """服务健康状态"""
        stats = state_manager.get_stats()
        return {
            "status": "ok",
            "message": "服务运行正常",
            "version": config.version,
            "sessions": stats["sessions"],
            "tasks": stats["tasks"],
            "active_tasks": stats["active_tasks"],
            "websocket_connections": stats["websocket_connections"]
        }

    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=config.host, port=config.port)
